# 🔧 Health Dashboard Troubleshooting Guide

## Quick Start Checklist

### 1. **Install Dependencies**
```bash
# Navigate to the project directory
cd data-playbook

# Install dependencies (try in this order)
npm install
# OR
yarn install
# OR
pnpm install
# OR
bun install
```

### 2. **Start Development Server**
```bash
# Start the development server
npm run dev
# OR
yarn dev
# OR
pnpm dev
# OR
bun dev
```

### 3. **Open in Browser**
- Open `http://localhost:5173` (or the port shown in terminal)
- You should see the login screen

---

## Common Issues & Solutions

### ❌ Issue: "npm: command not found"
**Solution:** Install Node.js and npm
```bash
# Install Node.js (includes npm)
# Visit: https://nodejs.org/
# OR use a package manager:

# macOS (using Homebrew)
brew install node

# Ubuntu/Debian
sudo apt update && sudo apt install nodejs npm

# Windows (using Chocolatey)
choco install nodejs
```

### ❌ Issue: "Cannot find module" errors
**Solution:** Clear cache and reinstall
```bash
# Delete node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
npm install
```

### ❌ Issue: "Port already in use"
**Solution:** Use a different port
```bash
# Kill process on port 5173
npx kill-port 5173

# OR start on different port
npm run dev -- --port 3000
```

### ❌ Issue: TypeScript errors
**Solution:** Check our enhanced components
```bash
# Run TypeScript check
npx tsc --noEmit

# Common fixes:
# 1. Ensure all imports are correct
# 2. Check if new dependencies are installed
# 3. Restart TypeScript server in VS Code: Cmd+Shift+P > "TypeScript: Restart TS Server"
```

### ❌ Issue: Supabase connection errors
**Solution:** Check environment variables
```bash
# Create .env.local file with your Supabase credentials
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

---

## 🎯 Enhanced Features Verification

### Test the New Components:

1. **Bento Grid Layout**
   - ✅ Should see 4-column grid on desktop
   - ✅ Should see 2-column grid on tablet
   - ✅ Should see 1-column grid on mobile

2. **Color-Coded Health Status**
   - ✅ Green cards = Excellent health metrics
   - ✅ Yellow cards = Good health metrics  
   - ✅ Orange cards = Warning health metrics
   - ✅ Red cards = Critical health metrics

3. **Organ System View**
   - ✅ Collapsible sections for each organ system
   - ✅ Cardiovascular, Nervous, Circulatory, Musculoskeletal, Metabolic
   - ✅ 2-column parameter grid within each section

4. **Enhanced PDF Upload**
   - ✅ Upload PDF health reports
   - ✅ See data preview with confidence scores
   - ✅ Edit extracted values before generating report

5. **Fun Facts**
   - ✅ Creative comparisons (e.g., "steps = climbing Eiffel Tower X times")
   - ✅ Horizontal layout with icons
   - ✅ Maximum 5 facts displayed

6. **Workout & Diet Plans**
   - ✅ Workout: Horizontal scroll cards with intensity colors
   - ✅ Diet: Collapsible table format with nutrition breakdown

---

## 🚀 Performance Optimization

### If the app feels slow:

1. **Enable React DevTools**
   ```bash
   # Install React DevTools browser extension
   # Check for unnecessary re-renders
   ```

2. **Check Bundle Size**
   ```bash
   # Analyze bundle
   npm run build
   npx vite-bundle-analyzer dist
   ```

3. **Optimize Images**
   - Ensure images are properly compressed
   - Use WebP format when possible

---

## 🔍 Debugging Steps

### 1. Check Browser Console
- Open DevTools (F12)
- Look for JavaScript errors in Console tab
- Check Network tab for failed requests

### 2. Verify Component Imports
```typescript
// Ensure these imports work:
import { MetricCard } from "@/components/MetricCard";
import { MetricWithTrend } from "@/components/MetricWithTrend";
import { DataPreview } from "@/components/DataPreview";
import { generateCreativeFunFacts } from "@/components/FunFactCard";
```

### 3. Test Individual Components
```bash
# Create a simple test page
# Copy the test.html file we created to verify styling
```

---

## 📱 Mobile Testing

### Test Responsive Design:
1. Open DevTools (F12)
2. Click device toggle icon
3. Test different screen sizes:
   - Mobile: 375px width
   - Tablet: 768px width  
   - Desktop: 1200px+ width

---

## 🆘 Still Having Issues?

### Check These Files:
1. `package.json` - Ensure all dependencies are listed
2. `vite.config.ts` - Verify build configuration
3. `tsconfig.json` - Check TypeScript settings
4. `.env.local` - Verify environment variables

### Get Help:
1. Check the browser's developer console for errors
2. Look at the terminal output for build errors
3. Verify all new components are properly exported
4. Test with sample data first before using real health data

---

## ✅ Success Indicators

You'll know everything is working when you see:
- ✅ Login screen loads without errors
- ✅ Can upload health data (file or manual)
- ✅ Health report generates with new Bento grid layout
- ✅ All metrics show proper color coding
- ✅ Organ systems are collapsible
- ✅ Fun facts display with creative comparisons
- ✅ Workout/diet plans show structured layouts
- ✅ Chat panel works for health questions
- ✅ Report regeneration creates new insights

---

## 🎉 Enjoy Your Enhanced Health Dashboard!

The dashboard now features:
- Professional Bento grid layout
- Color-coded health status system
- Enhanced PDF data extraction
- Creative fun facts with comparisons
- Structured workout and diet plans
- Interactive chat integration
- Social sharing capabilities
- Report regeneration functionality

Happy health tracking! 🏥📊
