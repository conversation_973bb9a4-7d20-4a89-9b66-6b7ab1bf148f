<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #ecfdf5 100%);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin: 16px 0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .metric-card.excellent {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #166534;
        }
        .metric-card.good {
            background: #fefce8;
            border-color: #fde047;
            color: #a16207;
        }
        .metric-card.warning {
            background: #fff7ed;
            border-color: #fed7aa;
            color: #c2410c;
        }
        .metric-card.critical {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .metric-label {
            font-size: 0.875rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        .metric-description {
            font-size: 0.875rem;
            opacity: 0.8;
            margin-top: 10px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: capitalize;
        }
        .status-excellent { background: #dcfce7; color: #166534; }
        .status-good { background: #fef3c7; color: #a16207; }
        .status-warning { background: #fed7aa; color: #c2410c; }
        .status-critical { background: #fecaca; color: #dc2626; }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            color: #6b7280;
            font-size: 1.1rem;
        }
        .fun-fact {
            background: linear-gradient(135deg, #dbeafe, #f3e8ff);
            border: 2px solid #c7d2fe;
            border-radius: 12px;
            padding: 16px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .fun-fact-icon {
            font-size: 2rem;
        }
        .fun-fact-content {
            flex: 1;
        }
        .fun-fact-metric {
            font-weight: 600;
            color: #3730a3;
            font-size: 0.875rem;
        }
        .fun-fact-text {
            color: #374151;
            font-size: 0.875rem;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🏥 Smart Health Dashboard</h1>
            <p class="subtitle">Your comprehensive health metrics at a glance</p>
        </div>

        <div class="card">
            <h2>📊 Health Metrics Dashboard</h2>
            <p>Premium Bento Grid Layout - 4 columns desktop, 2 tablet, 1 mobile</p>
        </div>

        <div class="grid">
            <div class="metric-card excellent">
                <div class="metric-label">Daily Steps</div>
                <div class="metric-value">12,345</div>
                <span class="status-badge status-excellent">excellent</span>
                <div class="metric-description">Great activity! Supports heart health.</div>
            </div>

            <div class="metric-card good">
                <div class="metric-label">Distance</div>
                <div class="metric-value">8.2 km</div>
                <span class="status-badge status-good">good</span>
                <div class="metric-description">Good distance covered today!</div>
            </div>

            <div class="metric-card warning">
                <div class="metric-label">Sleep Hours</div>
                <div class="metric-value">6.5 hours</div>
                <span class="status-badge status-warning">warning</span>
                <div class="metric-description">Aim for 7-9 hours nightly.</div>
            </div>

            <div class="metric-card excellent">
                <div class="metric-label">Heart Rate</div>
                <div class="metric-value">72 BPM</div>
                <span class="status-badge status-excellent">excellent</span>
                <div class="metric-description">Healthy resting rate.</div>
            </div>

            <div class="metric-card good">
                <div class="metric-label">Hydration</div>
                <div class="metric-value">2.8 L</div>
                <span class="status-badge status-good">good</span>
                <div class="metric-description">Well hydrated today!</div>
            </div>

            <div class="metric-card excellent">
                <div class="metric-label">Calories Burned</div>
                <div class="metric-value">2,150 kcal</div>
                <span class="status-badge status-excellent">excellent</span>
                <div class="metric-description">Active energy expenditure.</div>
            </div>
        </div>

        <div class="card">
            <h2>🎉 Fun Health Facts</h2>
            <p>Creative comparisons with horizontal layout (max 5 facts)</p>
            
            <div class="fun-fact">
                <div class="fun-fact-icon">🗼</div>
                <div class="fun-fact-content">
                    <div class="fun-fact-metric">Daily Steps</div>
                    <div class="fun-fact-text">12,345 steps = climbing Eiffel Tower 7 times</div>
                </div>
            </div>

            <div class="fun-fact">
                <div class="fun-fact-icon">🔋</div>
                <div class="fun-fact-content">
                    <div class="fun-fact-metric">Calories Burned</div>
                    <div class="fun-fact-text">2,150 kcal = powering your phone for 215 hours</div>
                </div>
            </div>

            <div class="fun-fact">
                <div class="fun-fact-icon">🏃‍♂️</div>
                <div class="fun-fact-content">
                    <div class="fun-fact-metric">Distance Covered</div>
                    <div class="fun-fact-text">8.2 km = 0.19 marathon distances</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>✅ Implementation Status</h2>
            <ul style="line-height: 1.8;">
                <li>✅ Premium Bento Grid Layout (4-col desktop, 2-col tablet, 1-col mobile)</li>
                <li>✅ Health Status Color System (green/yellow/orange/red)</li>
                <li>✅ Organ System Categorization with collapsible sections</li>
                <li>✅ Concise Content Strategy (max 30 words per feedback)</li>
                <li>✅ Enhanced PDF Data Extraction with validation</li>
                <li>✅ Streamlined Fun Facts with creative comparisons</li>
                <li>✅ Structured Workout & Diet Plan displays</li>
                <li>✅ Interactive Chat Integration</li>
                <li>✅ Report Regeneration functionality</li>
                <li>✅ Social Sharing Features</li>
            </ul>
        </div>

        <div class="card">
            <h2>🚀 Next Steps</h2>
            <p>To see the full interactive dashboard:</p>
            <ol style="line-height: 1.8;">
                <li>Install dependencies: <code>npm install</code> or <code>yarn install</code></li>
                <li>Start development server: <code>npm run dev</code> or <code>yarn dev</code></li>
                <li>Open browser to <code>http://localhost:5173</code></li>
                <li>Sign up/login to access the dashboard</li>
                <li>Upload health data or use sample data</li>
                <li>Generate your enhanced health report!</li>
            </ol>
        </div>
    </div>
</body>
</html>
