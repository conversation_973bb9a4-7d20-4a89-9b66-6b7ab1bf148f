import { HealthData } from "@/pages/Index";

// Enhanced PDF text extraction utility
export interface ExtractedHealthData extends HealthData {
  confidence?: Record<string, number>;
  extractedText?: string;
}

// Health parameter patterns for text extraction
const HEALTH_PATTERNS = {
  steps: /(?:steps?|walking)\s*:?\s*(\d{1,6})/i,
  distance: /(?:distance|km|miles?)\s*:?\s*(\d+\.?\d*)\s*(?:km|miles?)?/i,
  calories: /(?:calories?|kcal|cal)\s*:?\s*(\d{1,5})/i,
  sleep: /(?:sleep|hours?)\s*:?\s*(\d+\.?\d*)\s*(?:hours?|hrs?|h)?/i,
  water: /(?:water|hydration|liters?|litres?)\s*:?\s*(\d+\.?\d*)\s*(?:l|liters?|litres?)?/i,
  heartRate: /(?:heart\s*rate|hr|bpm)\s*:?\s*(\d{2,3})\s*(?:bpm)?/i,
  weight: /(?:weight|kg|lbs?)\s*:?\s*(\d+\.?\d*)\s*(?:kg|lbs?)?/i,
  bloodPressure: /(?:blood\s*pressure|bp)\s*:?\s*(\d{2,3})\s*\/\s*(\d{2,3})/i,
  hemoglobin: /(?:hemoglobin|hb|haemoglobin)\s*:?\s*(\d+\.?\d*)\s*(?:g\/dl|mg\/dl)?/i,
  cholesterol: /(?:cholesterol|chol)\s*:?\s*(\d{2,3})\s*(?:mg\/dl)?/i,
  glucose: /(?:glucose|blood\s*sugar|bg)\s*:?\s*(\d{2,3})\s*(?:mg\/dl)?/i,
  vitaminD: /(?:vitamin\s*d|vit\s*d)\s*:?\s*(\d{1,3})\s*(?:ng\/ml|nmol\/l)?/i,
};

// Confidence scoring based on pattern matches
export const calculateConfidence = (text: string, pattern: RegExp): number => {
  const matches = text.match(pattern);
  if (!matches) return 0;
  
  // Higher confidence for more specific patterns
  let confidence = 0.7;
  
  // Boost confidence if units are present
  if (pattern.source.includes('bpm|kg|mg\/dl|g\/dl')) confidence += 0.2;
  
  // Boost confidence if context words are present
  const contextWords = ['test', 'result', 'level', 'count', 'measurement'];
  if (contextWords.some(word => text.toLowerCase().includes(word))) {
    confidence += 0.1;
  }
  
  return Math.min(confidence, 1.0);
};

// Extract health data from PDF text
export const extractHealthDataFromText = (text: string): ExtractedHealthData => {
  const extractedData: ExtractedHealthData = {
    extractedText: text,
    confidence: {}
  };

  // Extract basic metrics
  const stepsMatch = text.match(HEALTH_PATTERNS.steps);
  if (stepsMatch) {
    extractedData.steps = parseInt(stepsMatch[1]);
    extractedData.confidence!.steps = calculateConfidence(text, HEALTH_PATTERNS.steps);
  }

  const distanceMatch = text.match(HEALTH_PATTERNS.distance);
  if (distanceMatch) {
    extractedData.distance_km = parseFloat(distanceMatch[1]);
    extractedData.confidence!.distance_km = calculateConfidence(text, HEALTH_PATTERNS.distance);
  }

  const caloriesMatch = text.match(HEALTH_PATTERNS.calories);
  if (caloriesMatch) {
    extractedData.calories_kcal = parseInt(caloriesMatch[1]);
    extractedData.confidence!.calories_kcal = calculateConfidence(text, HEALTH_PATTERNS.calories);
  }

  const sleepMatch = text.match(HEALTH_PATTERNS.sleep);
  if (sleepMatch) {
    extractedData.sleep_hours = parseFloat(sleepMatch[1]);
    extractedData.confidence!.sleep_hours = calculateConfidence(text, HEALTH_PATTERNS.sleep);
  }

  const waterMatch = text.match(HEALTH_PATTERNS.water);
  if (waterMatch) {
    extractedData.water_liters = parseFloat(waterMatch[1]);
    extractedData.confidence!.water_liters = calculateConfidence(text, HEALTH_PATTERNS.water);
  }

  const heartRateMatch = text.match(HEALTH_PATTERNS.heartRate);
  if (heartRateMatch) {
    extractedData.heart_rate_bpm = parseInt(heartRateMatch[1]);
    extractedData.confidence!.heart_rate_bpm = calculateConfidence(text, HEALTH_PATTERNS.heartRate);
  }

  const weightMatch = text.match(HEALTH_PATTERNS.weight);
  if (weightMatch) {
    extractedData.weight_kg = parseFloat(weightMatch[1]);
    extractedData.confidence!.weight_kg = calculateConfidence(text, HEALTH_PATTERNS.weight);
  }

  // Extract blood pressure
  const bpMatch = text.match(HEALTH_PATTERNS.bloodPressure);
  if (bpMatch) {
    extractedData.blood_pressure = {
      systolic: parseInt(bpMatch[1]),
      diastolic: parseInt(bpMatch[2])
    };
    extractedData.confidence!.blood_pressure = calculateConfidence(text, HEALTH_PATTERNS.bloodPressure);
  }

  // Extract lab results
  const labResults: any = {};
  let hasLabResults = false;

  const hemoglobinMatch = text.match(HEALTH_PATTERNS.hemoglobin);
  if (hemoglobinMatch) {
    labResults.hemoglobin = parseFloat(hemoglobinMatch[1]);
    extractedData.confidence!.hemoglobin = calculateConfidence(text, HEALTH_PATTERNS.hemoglobin);
    hasLabResults = true;
  }

  const cholesterolMatch = text.match(HEALTH_PATTERNS.cholesterol);
  if (cholesterolMatch) {
    labResults.cholesterol = parseInt(cholesterolMatch[1]);
    extractedData.confidence!.cholesterol = calculateConfidence(text, HEALTH_PATTERNS.cholesterol);
    hasLabResults = true;
  }

  const glucoseMatch = text.match(HEALTH_PATTERNS.glucose);
  if (glucoseMatch) {
    labResults.glucose = parseInt(glucoseMatch[1]);
    extractedData.confidence!.glucose = calculateConfidence(text, HEALTH_PATTERNS.glucose);
    hasLabResults = true;
  }

  const vitaminDMatch = text.match(HEALTH_PATTERNS.vitaminD);
  if (vitaminDMatch) {
    labResults.vitamin_d = parseInt(vitaminDMatch[1]);
    extractedData.confidence!.vitamin_d = calculateConfidence(text, HEALTH_PATTERNS.vitaminD);
    hasLabResults = true;
  }

  if (hasLabResults) {
    extractedData.lab_results = labResults;
  }

  return extractedData;
};

// Validate extracted values against expected ranges
export const validateHealthData = (data: ExtractedHealthData): ExtractedHealthData => {
  const validated = { ...data };

  // Validate steps (reasonable range: 0-50000)
  if (validated.steps && (validated.steps < 0 || validated.steps > 50000)) {
    delete validated.steps;
    if (validated.confidence) delete validated.confidence.steps;
  }

  // Validate distance (reasonable range: 0-100 km)
  if (validated.distance_km && (validated.distance_km < 0 || validated.distance_km > 100)) {
    delete validated.distance_km;
    if (validated.confidence) delete validated.confidence.distance_km;
  }

  // Validate calories (reasonable range: 0-10000)
  if (validated.calories_kcal && (validated.calories_kcal < 0 || validated.calories_kcal > 10000)) {
    delete validated.calories_kcal;
    if (validated.confidence) delete validated.confidence.calories_kcal;
  }

  // Validate sleep (reasonable range: 0-24 hours)
  if (validated.sleep_hours && (validated.sleep_hours < 0 || validated.sleep_hours > 24)) {
    delete validated.sleep_hours;
    if (validated.confidence) delete validated.confidence.sleep_hours;
  }

  // Validate water (reasonable range: 0-10 liters)
  if (validated.water_liters && (validated.water_liters < 0 || validated.water_liters > 10)) {
    delete validated.water_liters;
    if (validated.confidence) delete validated.confidence.water_liters;
  }

  // Validate heart rate (reasonable range: 30-200 bpm)
  if (validated.heart_rate_bpm && (validated.heart_rate_bpm < 30 || validated.heart_rate_bpm > 200)) {
    delete validated.heart_rate_bpm;
    if (validated.confidence) delete validated.confidence.heart_rate_bpm;
  }

  // Validate weight (reasonable range: 20-300 kg)
  if (validated.weight_kg && (validated.weight_kg < 20 || validated.weight_kg > 300)) {
    delete validated.weight_kg;
    if (validated.confidence) delete validated.confidence.weight_kg;
  }

  // Validate blood pressure
  if (validated.blood_pressure) {
    const { systolic, diastolic } = validated.blood_pressure;
    if (systolic < 60 || systolic > 250 || diastolic < 40 || diastolic > 150) {
      delete validated.blood_pressure;
      if (validated.confidence) delete validated.confidence.blood_pressure;
    }
  }

  // Validate lab results
  if (validated.lab_results && typeof validated.lab_results === 'object') {
    const labResults = validated.lab_results as any;
    
    // Validate hemoglobin (reasonable range: 8-20 g/dL)
    if (labResults.hemoglobin && (labResults.hemoglobin < 8 || labResults.hemoglobin > 20)) {
      delete labResults.hemoglobin;
      if (validated.confidence) delete validated.confidence.hemoglobin;
    }

    // Validate cholesterol (reasonable range: 100-400 mg/dL)
    if (labResults.cholesterol && (labResults.cholesterol < 100 || labResults.cholesterol > 400)) {
      delete labResults.cholesterol;
      if (validated.confidence) delete validated.confidence.cholesterol;
    }

    // Validate glucose (reasonable range: 50-300 mg/dL)
    if (labResults.glucose && (labResults.glucose < 50 || labResults.glucose > 300)) {
      delete labResults.glucose;
      if (validated.confidence) delete validated.confidence.glucose;
    }

    // Validate vitamin D (reasonable range: 5-150 ng/mL)
    if (labResults.vitamin_d && (labResults.vitamin_d < 5 || labResults.vitamin_d > 150)) {
      delete labResults.vitamin_d;
      if (validated.confidence) delete validated.confidence.vitamin_d;
    }
  }

  return validated;
};

// Mock PDF text extraction (replace with actual PDF.js implementation when dependencies are available)
export const extractTextFromPDF = async (file: File): Promise<string> => {
  // This is a placeholder implementation
  // In a real implementation, you would use PDF.js to extract text
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = () => {
      // For now, return a mock extracted text
      resolve(`
        Health Report Summary
        Steps: 12,345
        Distance: 8.2 km
        Calories: 2,150 kcal
        Sleep: 7.5 hours
        Water: 2.8 liters
        Heart Rate: 72 bpm
        Weight: 70 kg
        Blood Pressure: 120/80
        
        Lab Results:
        Hemoglobin: 14.2 g/dL
        Cholesterol: 185 mg/dL
        Glucose: 95 mg/dL
        Vitamin D: 32 ng/mL
      `);
    };
    reader.readAsText(file);
  });
};
