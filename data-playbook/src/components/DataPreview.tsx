import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  CheckCircle, 
  AlertCircle, 
  Edit3, 
  Save, 
  X,
  Activity,
  Heart,
  Footprints,
  Moon,
  Droplets,
  Scale,
  Gauge
} from "lucide-react";
import { HealthData } from "@/pages/Index";
import { ExtractedHealthData } from "@/utils/pdfExtraction";
import { toast } from "sonner";

interface DataPreviewProps {
  extractedData: ExtractedHealthData;
  onDataConfirmed: (data: HealthData) => void;
  onCancel: () => void;
}

interface EditableField {
  key: string;
  label: string;
  value: any;
  unit?: string;
  icon: any;
  confidence?: number;
  isEditing: boolean;
}

export const DataPreview = ({ extractedData, onDataConfirmed, onCancel }: DataPreviewProps) => {
  const [editableFields, setEditableFields] = useState<EditableField[]>([]);
  const [labResults, setLabResults] = useState<any>({});

  useEffect(() => {
    initializeFields();
  }, [extractedData]);

  const initializeFields = () => {
    const fields: EditableField[] = [
      {
        key: 'steps',
        label: 'Daily Steps',
        value: extractedData.steps || '',
        unit: 'steps',
        icon: Footprints,
        confidence: extractedData.confidence?.steps,
        isEditing: false
      },
      {
        key: 'distance_km',
        label: 'Distance',
        value: extractedData.distance_km || '',
        unit: 'km',
        icon: Activity,
        confidence: extractedData.confidence?.distance_km,
        isEditing: false
      },
      {
        key: 'calories_kcal',
        label: 'Calories Burned',
        value: extractedData.calories_kcal || '',
        unit: 'kcal',
        icon: Activity,
        confidence: extractedData.confidence?.calories_kcal,
        isEditing: false
      },
      {
        key: 'sleep_hours',
        label: 'Sleep Hours',
        value: extractedData.sleep_hours || '',
        unit: 'hours',
        icon: Moon,
        confidence: extractedData.confidence?.sleep_hours,
        isEditing: false
      },
      {
        key: 'water_liters',
        label: 'Water Intake',
        value: extractedData.water_liters || '',
        unit: 'liters',
        icon: Droplets,
        confidence: extractedData.confidence?.water_liters,
        isEditing: false
      },
      {
        key: 'heart_rate_bpm',
        label: 'Heart Rate',
        value: extractedData.heart_rate_bpm || '',
        unit: 'bpm',
        icon: Heart,
        confidence: extractedData.confidence?.heart_rate_bpm,
        isEditing: false
      },
      {
        key: 'weight_kg',
        label: 'Weight',
        value: extractedData.weight_kg || '',
        unit: 'kg',
        icon: Scale,
        confidence: extractedData.confidence?.weight_kg,
        isEditing: false
      }
    ];

    setEditableFields(fields);

    // Initialize lab results
    if (extractedData.lab_results && typeof extractedData.lab_results === 'object') {
      setLabResults(extractedData.lab_results);
    }
  };

  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'bg-gray-100 text-gray-600';
    if (confidence >= 0.8) return 'bg-green-100 text-green-700';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-700';
    return 'bg-red-100 text-red-700';
  };

  const getConfidenceText = (confidence?: number) => {
    if (!confidence) return 'Manual';
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    return 'Low';
  };

  const toggleEdit = (index: number) => {
    setEditableFields(prev => prev.map((field, i) => 
      i === index ? { ...field, isEditing: !field.isEditing } : field
    ));
  };

  const updateFieldValue = (index: number, newValue: string) => {
    setEditableFields(prev => prev.map((field, i) => 
      i === index ? { ...field, value: newValue } : field
    ));
  };

  const updateLabResult = (key: string, value: string) => {
    setLabResults((prev: any) => ({
      ...prev,
      [key]: value ? parseFloat(value) : undefined
    }));
  };

  const handleConfirm = () => {
    const confirmedData: HealthData = {};

    // Process basic fields
    editableFields.forEach(field => {
      if (field.value && field.value !== '') {
        const numericValue = parseFloat(field.value);
        if (!isNaN(numericValue)) {
          (confirmedData as any)[field.key] = numericValue;
        }
      }
    });

    // Process blood pressure if available
    if (extractedData.blood_pressure) {
      confirmedData.blood_pressure = extractedData.blood_pressure;
    }

    // Process lab results
    const filteredLabResults = Object.entries(labResults)
      .filter(([_, value]) => value !== undefined && value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(filteredLabResults).length > 0) {
      confirmedData.lab_results = filteredLabResults;
    }

    toast.success("Health data confirmed!");
    onDataConfirmed(confirmedData);
  };

  const IconComponent = ({ icon: Icon }: { icon: any }) => <Icon className="w-5 h-5" />;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-6 h-6 text-green-600" />
            Review Extracted Health Data
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Please review and edit the extracted values before generating your health report.
            Click the edit icon to modify any values.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Health Metrics */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Basic Health Metrics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {editableFields.map((field, index) => (
                <div key={field.key} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <IconComponent icon={field.icon} />
                      <span className="font-medium">{field.label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {field.confidence && (
                        <Badge 
                          variant="outline" 
                          className={getConfidenceColor(field.confidence)}
                        >
                          {getConfidenceText(field.confidence)}
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleEdit(index)}
                      >
                        {field.isEditing ? <Save className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {field.isEditing ? (
                      <Input
                        type="number"
                        value={field.value}
                        onChange={(e) => updateFieldValue(index, e.target.value)}
                        placeholder={`Enter ${field.label.toLowerCase()}`}
                        className="flex-1"
                      />
                    ) : (
                      <div className="flex-1">
                        {field.value ? (
                          <span className="text-lg font-semibold">
                            {field.value} {field.unit}
                          </span>
                        ) : (
                          <span className="text-muted-foreground italic">
                            No data available
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Blood Pressure */}
          {extractedData.blood_pressure && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Blood Pressure</h3>
              <div className="border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Gauge className="w-5 h-5" />
                  <span className="font-medium">Blood Pressure</span>
                  {extractedData.confidence?.blood_pressure && (
                    <Badge 
                      variant="outline" 
                      className={getConfidenceColor(extractedData.confidence.blood_pressure)}
                    >
                      {getConfidenceText(extractedData.confidence.blood_pressure)}
                    </Badge>
                  )}
                </div>
                <span className="text-lg font-semibold">
                  {extractedData.blood_pressure.systolic}/{extractedData.blood_pressure.diastolic} mmHg
                </span>
              </div>
            </div>
          )}

          {/* Lab Results */}
          {Object.keys(labResults).length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Lab Results</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(labResults).map(([key, value]) => (
                  <div key={key} className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="font-medium capitalize">
                        {key.replace('_', ' ')}
                      </Label>
                      {extractedData.confidence?.[key] && (
                        <Badge 
                          variant="outline" 
                          className={getConfidenceColor(extractedData.confidence[key])}
                        >
                          {getConfidenceText(extractedData.confidence[key])}
                        </Badge>
                      )}
                    </div>
                    <Input
                      type="number"
                      value={value || ''}
                      onChange={(e) => updateLabResult(key, e.target.value)}
                      placeholder={`Enter ${key.replace('_', ' ')}`}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Extracted Text Preview */}
          {extractedData.extractedText && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Extracted Text Preview</h3>
              <div className="bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto">
                <pre className="text-sm whitespace-pre-wrap">
                  {extractedData.extractedText.substring(0, 500)}
                  {extractedData.extractedText.length > 500 && '...'}
                </pre>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 justify-end pt-4">
            <Button variant="outline" onClick={onCancel}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleConfirm}>
              <CheckCircle className="w-4 h-4 mr-2" />
              Confirm & Generate Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
