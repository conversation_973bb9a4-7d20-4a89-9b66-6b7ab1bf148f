import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LucideIcon } from "lucide-react";
import { LineChart, Line, ResponsiveContainer, XAxis, YAxis, Tooltip } from "recharts";

interface MetricWithTrendProps {
  metric: {
    id: string;
    label: string;
    value: string;
    status: 'excellent' | 'good' | 'warning' | 'critical';
    icon: LucideIcon;
    description: string;
  };
  trendData?: Array<{ date: string; value: number; label?: string }>;
  showTrend?: boolean;
}

export const MetricWithTrend = ({ metric, trendData, showTrend = true }: MetricWithTrendProps) => {
  // Color system matching MetricCard
  const getStatusColors = (status: string) => {
    switch (status) {
      case 'excellent': 
        return {
          background: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-800',
          icon: 'bg-green-500',
          line: '#22c55e'
        };
      case 'good': 
        return {
          background: 'bg-yellow-50',
          border: 'border-yellow-200',
          text: 'text-yellow-800',
          icon: 'bg-yellow-500',
          line: '#eab308'
        };
      case 'warning': 
        return {
          background: 'bg-orange-50',
          border: 'border-orange-200',
          text: 'text-orange-800',
          icon: 'bg-orange-500',
          line: '#f97316'
        };
      case 'critical': 
        return {
          background: 'bg-red-50',
          border: 'border-red-200',
          text: 'text-red-800',
          icon: 'bg-red-500',
          line: '#ef4444'
        };
      default: 
        return {
          background: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-800',
          icon: 'bg-gray-500',
          line: '#6b7280'
        };
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'excellent': return 'default';
      case 'good': return 'secondary';
      case 'warning': return 'outline';
      case 'critical': return 'destructive';
      default: return 'secondary';
    }
  };

  const colors = getStatusColors(metric.status);
  const IconComponent = metric.icon;

  // Truncate description to max 15 words for AI insights
  const truncateDescription = (text: string, maxWords: number = 15) => {
    const words = text.split(' ');
    if (words.length <= maxWords) return text;
    return words.slice(0, maxWords).join(' ') + '...';
  };

  // Generate mock trend data if none provided
  const generateMockTrendData = () => {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const baseValue = parseFloat(metric.value.replace(/[^\d.]/g, '')) || 100;
    
    return days.map((day, index) => ({
      date: day,
      value: baseValue + (Math.random() - 0.5) * baseValue * 0.2,
      label: day
    }));
  };

  const chartData = trendData || generateMockTrendData();

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow-lg">
          <p className="text-sm font-medium">{label}</p>
          <p className="text-sm" style={{ color: colors.line }}>
            {`${metric.label}: ${payload[0].value.toFixed(1)}`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={`p-6 transition-all duration-300 hover:shadow-lg ${colors.background} ${colors.border} ${colors.text}`}>
      <div className="space-y-4">
        {/* Header with icon and status */}
        <div className="flex items-center justify-between">
          <div className={`p-3 rounded-xl ${colors.icon}`}>
            <IconComponent className="w-6 h-6 text-white" />
          </div>
          <Badge variant={getStatusBadgeVariant(metric.status)} className="capitalize">
            {metric.status}
          </Badge>
        </div>

        {/* Metric value */}
        <div className="space-y-1">
          <p className="text-sm font-medium opacity-70">{metric.label}</p>
          <p className="text-3xl font-bold leading-none">{metric.value}</p>
        </div>

        {/* AI Insight - max 15 words */}
        <p className="text-sm opacity-80 leading-relaxed">
          {truncateDescription(metric.description, 15)}
        </p>

        {/* Trend Chart */}
        {showTrend && chartData.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium opacity-60">7-Day Trend</span>
              <span className="text-xs opacity-50">
                {chartData.length} data points
              </span>
            </div>
            <div className="h-[120px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData}>
                  <XAxis 
                    dataKey="date" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 10, fill: colors.text }}
                  />
                  <YAxis hide />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke={colors.line}
                    strokeWidth={2}
                    dot={{ fill: colors.line, strokeWidth: 0, r: 3 }}
                    activeDot={{ r: 4, stroke: colors.line, strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

// Standalone trend chart component for larger displays
export const TrendChart = ({ 
  data, 
  title, 
  color = "#3b82f6",
  height = 200 
}: {
  data: Array<{ date: string; value: number; label?: string }>;
  title: string;
  color?: string;
  height?: number;
}) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded shadow-lg">
          <p className="text-sm font-medium">{label}</p>
          <p className="text-sm" style={{ color }}>
            {`${title}: ${payload[0].value.toFixed(1)}`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="p-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">{title} Trend</h3>
        <div style={{ height: `${height}px` }} className="w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data}>
              <XAxis 
                dataKey="date" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="value"
                stroke={color}
                strokeWidth={3}
                dot={{ fill: color, strokeWidth: 0, r: 4 }}
                activeDot={{ r: 6, stroke: color, strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </Card>
  );
};
