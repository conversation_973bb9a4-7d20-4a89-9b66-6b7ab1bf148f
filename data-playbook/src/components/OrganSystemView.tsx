import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import {
  Heart,
  Brain,
  Activity,
  Bone,
  Eye,
  Droplets,
  ChevronDown,
  ChevronRight,
  Zap,
  Scale
} from "lucide-react";
import { HealthData } from "@/pages/Index";
import { getLabValue } from "@/utils/labResultsHelpers";
import { useState } from "react";

interface OrganSystemViewProps {
  healthData: HealthData;
}

interface OrganSystem {
  name: string;
  icon: any;
  color: string;
  parameters: Array<{
    name: string;
    value: string;
    status: 'excellent' | 'good' | 'warning' | 'critical';
    comment: string;
  }>;
}

export const OrganSystemView = ({ healthData }: OrganSystemViewProps) => {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    cardiovascular: true,
    nervous: true,
    circulatory: true,
    musculoskeletal: true,
    metabolic: true
  });

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const getBloodPressureStatus = (systolic: number, diastolic: number): 'excellent' | 'good' | 'warning' | 'critical' => {
    if (systolic <= 120 && diastolic <= 80) return 'excellent';
    if (systolic <= 129 && diastolic <= 84) return 'good';
    if (systolic <= 139 || diastolic <= 89) return 'warning';
    return 'critical';
  };

  const getHeartRateStatus = (hr: number): 'excellent' | 'good' | 'warning' | 'critical' => {
    if (hr >= 60 && hr <= 75) return 'excellent';
    if (hr >= 50 && hr <= 85) return 'good';
    if (hr >= 40 && hr <= 100) return 'warning';
    return 'critical';
  };

  const getCholesterolStatus = (chol: number): 'excellent' | 'good' | 'warning' | 'critical' => {
    if (chol < 200) return 'excellent';
    if (chol < 240) return 'good';
    if (chol < 280) return 'warning';
    return 'critical';
  };

  const getGlucoseStatus = (glucose: number): 'excellent' | 'good' | 'warning' | 'critical' => {
    if (glucose >= 70 && glucose <= 100) return 'excellent';
    if (glucose >= 101 && glucose <= 125) return 'good';
    if (glucose >= 126 && glucose <= 140) return 'warning';
    return 'critical';
  };

  const getStepsStatus = (steps: number): 'excellent' | 'good' | 'warning' | 'critical' => {
    if (steps >= 10000) return 'excellent';
    if (steps >= 7500) return 'good';
    if (steps >= 5000) return 'warning';
    return 'critical';
  };

  const getVitaminDStatus = (vitD: number): 'excellent' | 'good' | 'warning' | 'critical' => {
    if (vitD >= 30) return 'excellent';
    if (vitD >= 20) return 'good';
    if (vitD >= 12) return 'warning';
    return 'critical';
  };

  const organSystems: OrganSystem[] = [
    {
      name: "Cardiovascular System",
      icon: Heart,
      color: "bg-red-100 text-red-600",
      parameters: [
        {
          name: "Heart Rate",
          value: healthData.heart_rate_bpm ? `${healthData.heart_rate_bpm} BPM` : "No data available",
          status: healthData.heart_rate_bpm ? getHeartRateStatus(healthData.heart_rate_bpm) : 'warning',
          comment: healthData.heart_rate_bpm ? "Resting HR optimal" : "Data needed"
        },
        {
          name: "Blood Pressure",
          value: healthData.blood_pressure ? `${healthData.blood_pressure.systolic}/${healthData.blood_pressure.diastolic} mmHg` : "No data available",
          status: healthData.blood_pressure ? getBloodPressureStatus(healthData.blood_pressure.systolic, healthData.blood_pressure.diastolic) : 'warning',
          comment: healthData.blood_pressure ? "BP within range" : "Monitor needed"
        },
        {
          name: "Total Cholesterol",
          value: getLabValue(healthData.lab_results, 'cholesterol') ? `${getLabValue(healthData.lab_results, 'cholesterol')} mg/dL` : "No data available",
          status: getLabValue(healthData.lab_results, 'cholesterol') ? getCholesterolStatus(getLabValue(healthData.lab_results, 'cholesterol')!) : 'warning',
          comment: getLabValue(healthData.lab_results, 'cholesterol') ? "Cholesterol healthy" : "Lab test needed"
        },
        {
          name: "Resting Heart Rate",
          value: healthData.heart_rate_bpm ? `${healthData.heart_rate_bpm} BPM` : "No data available",
          status: healthData.heart_rate_bpm ? getHeartRateStatus(healthData.heart_rate_bpm) : 'warning',
          comment: healthData.heart_rate_bpm ? "Cardio fitness good" : "Track daily"
        }
      ]
    },
    {
      name: "Nervous System",
      icon: Brain,
      color: "bg-purple-100 text-purple-600",
      parameters: [
        {
          name: "Sleep Quality",
          value: healthData.sleep_hours ? `${healthData.sleep_hours} hours` : "No data available",
          status: (healthData.sleep_hours || 0) >= 7 ? 'excellent' : 'warning',
          comment: (healthData.sleep_hours || 0) >= 7 ? "Sleep optimal" : "Need more rest"
        },
        {
          name: "Stress Level",
          value: "Moderate", // This would come from additional data
          status: 'good',
          comment: "Manageable stress"
        }
      ]
    },
    {
      name: "Circulatory System",
      icon: Droplets,
      color: "bg-blue-100 text-blue-600",
      parameters: [
        {
          name: "Hemoglobin",
          value: getLabValue(healthData.lab_results, 'hemoglobin') ? `${getLabValue(healthData.lab_results, 'hemoglobin')} g/dL` : "No data available",
          status: getLabValue(healthData.lab_results, 'hemoglobin') ? ((getLabValue(healthData.lab_results, 'hemoglobin')! >= 12) ? 'excellent' : 'warning') : 'warning',
          comment: getLabValue(healthData.lab_results, 'hemoglobin') ? "Oxygen transport good" : "Blood test needed"
        },
        {
          name: "Hydration",
          value: healthData.water_liters ? `${healthData.water_liters} L/day` : "No data available",
          status: (healthData.water_liters || 0) >= 2 ? 'excellent' : 'warning',
          comment: (healthData.water_liters || 0) >= 2 ? "Well hydrated" : "Drink more water"
        },
        {
          name: "RBC Count",
          value: "Normal", // This would come from lab results
          status: 'good',
          comment: "Blood cells healthy"
        },
        {
          name: "Iron Levels",
          value: "Adequate", // This would come from lab results
          status: 'good',
          comment: "Iron stores good"
        }
      ]
    },
    {
      name: "Musculoskeletal System",
      icon: Bone,
      color: "bg-orange-100 text-orange-600",
      parameters: [
        {
          name: "Daily Steps",
          value: healthData.steps ? `${healthData.steps.toLocaleString()} steps` : "No data available",
          status: healthData.steps ? getStepsStatus(healthData.steps) : 'warning',
          comment: healthData.steps ? (healthData.steps >= 10000 ? "Very active" : "Increase activity") : "Track movement"
        },
        {
          name: "Vitamin D",
          value: getLabValue(healthData.lab_results, 'vitamin_d') ? `${getLabValue(healthData.lab_results, 'vitamin_d')} ng/mL` : "No data available",
          status: getLabValue(healthData.lab_results, 'vitamin_d') ? getVitaminDStatus(getLabValue(healthData.lab_results, 'vitamin_d')!) : 'warning',
          comment: getLabValue(healthData.lab_results, 'vitamin_d') ? "Bone health supported" : "Sun exposure needed"
        },
        {
          name: "Bone Density",
          value: "Normal", // This would come from DEXA scan
          status: 'good',
          comment: "Bones strong"
        },
        {
          name: "Muscle Mass",
          value: "Adequate", // This would come from body composition
          status: 'good',
          comment: "Maintain strength"
        }
      ]
    },
    {
      name: "Metabolic System",
      icon: Zap,
      color: "bg-green-100 text-green-600",
      parameters: [
        {
          name: "Blood Glucose",
          value: getLabValue(healthData.lab_results, 'glucose') ? `${getLabValue(healthData.lab_results, 'glucose')} mg/dL` : "No data available",
          status: getLabValue(healthData.lab_results, 'glucose') ? getGlucoseStatus(getLabValue(healthData.lab_results, 'glucose')!) : 'warning',
          comment: getLabValue(healthData.lab_results, 'glucose') ? "Blood sugar stable" : "Monitor glucose"
        },
        {
          name: "Body Weight",
          value: healthData.weight_kg ? `${healthData.weight_kg} kg` : "No data available",
          status: 'good',
          comment: healthData.weight_kg ? "Weight stable" : "Track weight"
        },
        {
          name: "BMI",
          value: healthData.weight_kg ? `${(healthData.weight_kg / 1.75 / 1.75).toFixed(1)}` : "No data available", // Assuming 1.75m height
          status: 'good',
          comment: "Healthy range"
        },
        {
          name: "Calories Burned",
          value: healthData.calories_kcal ? `${healthData.calories_kcal.toLocaleString()} kcal` : "No data available",
          status: (healthData.calories_kcal || 0) > 2000 ? 'excellent' : 'good',
          comment: (healthData.calories_kcal || 0) > 2000 ? "Active metabolism" : "Increase activity"
        },
        {
          name: "Metabolism Rate",
          value: "Normal", // This would be calculated
          status: 'good',
          comment: "Efficient energy use"
        }
      ]
    }
  ];

  const getStatusColors = (status: string) => {
    switch (status) {
      case 'excellent':
        return {
          background: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-800',
          dot: 'bg-green-500'
        };
      case 'good':
        return {
          background: 'bg-yellow-50',
          border: 'border-yellow-200',
          text: 'text-yellow-800',
          dot: 'bg-yellow-500'
        };
      case 'warning':
        return {
          background: 'bg-orange-50',
          border: 'border-orange-200',
          text: 'text-orange-800',
          dot: 'bg-orange-500'
        };
      case 'critical':
        return {
          background: 'bg-red-50',
          border: 'border-red-200',
          text: 'text-red-800',
          dot: 'bg-red-500'
        };
      default:
        return {
          background: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-800',
          dot: 'bg-gray-500'
        };
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'excellent': return 'default';
      case 'good': return 'secondary';
      case 'warning': return 'outline';
      case 'critical': return 'destructive';
      default: return 'secondary';
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-center">Health Parameters by Organ System</h2>

      <div className="space-y-4">
        {organSystems.map((system, index) => {
          const IconComponent = system.icon;
          const sectionId = system.name.toLowerCase().replace(/\s+/g, '');
          const isOpen = openSections[sectionId];

          return (
            <Card key={index} className="overflow-hidden">
              <Collapsible
                open={isOpen}
                onOpenChange={() => toggleSection(sectionId)}
              >
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full p-6 justify-between hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-3 rounded-xl ${system.color}`}>
                        <IconComponent className="w-6 h-6" />
                      </div>
                      <h3 className="text-lg font-semibold">{system.name}</h3>
                      <Badge variant="outline" className="ml-2">
                        {system.parameters.length} metrics
                      </Badge>
                    </div>
                    {isOpen ? (
                      <ChevronDown className="w-5 h-5" />
                    ) : (
                      <ChevronRight className="w-5 h-5" />
                    )}
                  </Button>
                </CollapsibleTrigger>

                <CollapsibleContent className="px-6 pb-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {system.parameters.map((param, paramIndex) => {
                      const colors = getStatusColors(param.status);

                      return (
                        <div
                          key={paramIndex}
                          className={`border rounded-lg p-4 space-y-2 ${colors.background} ${colors.border} ${colors.text}`}
                        >
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{param.name}</h4>
                            <Badge variant={getStatusBadgeVariant(param.status)}>
                              {param.status}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-lg font-bold">{param.value}</span>
                            <div className={`w-3 h-3 rounded-full ${colors.dot}`}></div>
                          </div>
                          <p className="text-sm opacity-80">{param.comment}</p>
                        </div>
                      );
                    })}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
