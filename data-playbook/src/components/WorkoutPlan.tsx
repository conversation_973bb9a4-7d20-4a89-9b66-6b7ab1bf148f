
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dumbbell,
  Clock,
  Activity,
  Target,
  Calendar,
  Zap
} from "lucide-react";

interface WorkoutPlanProps {
  plan: Array<{
    day: string;
    activity: string;
    duration: string;
    intensity: string;
  }>;
}

export const WorkoutPlan = ({ plan }: WorkoutPlanProps) => {
  const getIntensityConfig = (intensity: string) => {
    switch (intensity.toLowerCase()) {
      case 'high':
        return {
          variant: 'destructive' as const,
          color: 'bg-red-100 border-red-300 text-red-800',
          icon: Zap
        };
      case 'moderate':
        return {
          variant: 'default' as const,
          color: 'bg-yellow-100 border-yellow-300 text-yellow-800',
          icon: Activity
        };
      case 'low':
        return {
          variant: 'secondary' as const,
          color: 'bg-blue-100 border-blue-300 text-blue-800',
          icon: Target
        };
      default:
        return {
          variant: 'outline' as const,
          color: 'bg-gray-100 border-gray-300 text-gray-800',
          icon: Activity
        };
    }
  };

  const getExerciseIcon = (activity: string) => {
    const activityLower = activity.toLowerCase();
    if (activityLower.includes('cardio') || activityLower.includes('running')) {
      return Activity;
    }
    if (activityLower.includes('strength') || activityLower.includes('weight')) {
      return Dumbbell;
    }
    return Target;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500">
            <Dumbbell className="w-5 h-5 text-white" />
          </div>
          <span>7-Day Workout Plan</span>
          <Badge variant="outline" className="ml-auto">
            {plan.length} days
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent>
        {/* Horizontal scroll layout for daily cards */}
        <ScrollArea className="w-full">
          <div className="flex gap-4 pb-4">
            {plan.map((workout, index) => {
              const intensityConfig = getIntensityConfig(workout.intensity);
              const ExerciseIcon = getExerciseIcon(workout.activity);
              const IntensityIcon = intensityConfig.icon;

              return (
                <div
                  key={index}
                  className={`flex-shrink-0 w-64 border-2 rounded-xl p-4 space-y-3 ${intensityConfig.color} transition-all duration-300 hover:shadow-lg`}
                >
                  {/* Day header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <h3 className="font-semibold">{workout.day}</h3>
                    </div>
                    <Badge variant={intensityConfig.variant} className="text-xs">
                      {workout.intensity}
                    </Badge>
                  </div>

                  {/* Exercise icon and activity */}
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-white/50">
                      <ExerciseIcon className="w-5 h-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium leading-tight">
                        {workout.activity}
                      </p>
                    </div>
                  </div>

                  {/* Duration and intensity */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs">
                      <Clock className="w-3 h-3" />
                      <span>{workout.duration}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs">
                      <IntensityIcon className="w-3 h-3" />
                      <span>{workout.intensity} intensity</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>

        {/* Summary stats */}
        <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{plan.length}</p>
            <p className="text-xs text-muted-foreground">Days</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              {plan.filter(w => w.intensity.toLowerCase() === 'high').length}
            </p>
            <p className="text-xs text-muted-foreground">High Intensity</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              {plan.reduce((total, w) => {
                const duration = parseInt(w.duration.match(/\d+/)?.[0] || '0');
                return total + duration;
              }, 0)}
            </p>
            <p className="text-xs text-muted-foreground">Total Minutes</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
