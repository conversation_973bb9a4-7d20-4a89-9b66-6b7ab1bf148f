
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LucideIcon } from "lucide-react";

interface MetricCardProps {
  metric: {
    id: string;
    label: string;
    value: string;
    status: 'excellent' | 'good' | 'warning' | 'critical';
    icon: LucideIcon;
    description: string;
  };
  showTrend?: boolean;
  trendData?: Array<{ date: string; value: number }>;
}

export const MetricCard = ({ metric, showTrend = false, trendData }: MetricCardProps) => {
  // New color system based on requirements
  const getStatusColors = (status: string) => {
    switch (status) {
      case 'excellent':
        return {
          background: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-800',
          icon: 'bg-green-500'
        };
      case 'good':
        return {
          background: 'bg-yellow-50',
          border: 'border-yellow-200',
          text: 'text-yellow-800',
          icon: 'bg-yellow-500'
        };
      case 'warning':
        return {
          background: 'bg-orange-50',
          border: 'border-orange-200',
          text: 'text-orange-800',
          icon: 'bg-orange-500'
        };
      case 'critical':
        return {
          background: 'bg-red-50',
          border: 'border-red-200',
          text: 'text-red-800',
          icon: 'bg-red-500'
        };
      default:
        return {
          background: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-800',
          icon: 'bg-gray-500'
        };
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'excellent': return 'default';
      case 'good': return 'secondary';
      case 'warning': return 'outline';
      case 'critical': return 'destructive';
      default: return 'secondary';
    }
  };

  const colors = getStatusColors(metric.status);
  const IconComponent = metric.icon;

  // Truncate description to max 30 words (2 lines)
  const truncateDescription = (text: string, maxWords: number = 30) => {
    const words = text.split(' ');
    if (words.length <= maxWords) return text;
    return words.slice(0, maxWords).join(' ') + '...';
  };

  return (
    <Card className={`p-6 transition-all duration-300 hover:shadow-lg ${colors.background} ${colors.border} ${colors.text}`}>
      <div className="space-y-4">
        {/* Header with icon and status */}
        <div className="flex items-center justify-between">
          <div className={`p-3 rounded-xl ${colors.icon}`}>
            <IconComponent className="w-6 h-6 text-white" />
          </div>
          <Badge variant={getStatusBadgeVariant(metric.status)} className="capitalize">
            {metric.status}
          </Badge>
        </div>

        {/* Metric value */}
        <div className="space-y-1">
          <p className="text-sm font-medium opacity-70">{metric.label}</p>
          <p className="text-3xl font-bold leading-none">{metric.value}</p>
        </div>

        {/* Description - max 2 lines */}
        <p className="text-sm opacity-80 leading-relaxed">
          {truncateDescription(metric.description)}
        </p>

        {/* Optional trend visualization placeholder */}
        {showTrend && trendData && (
          <div className="h-16 bg-white/20 rounded-lg flex items-center justify-center">
            <span className="text-xs opacity-60">Trend chart placeholder</span>
          </div>
        )}
      </div>
    </Card>
  );
};
