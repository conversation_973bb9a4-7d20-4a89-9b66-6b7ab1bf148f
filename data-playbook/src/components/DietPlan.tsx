
import { <PERSON>, <PERSON>H<PERSON>er, <PERSON><PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChefHat,
  Coffee,
  Utensils,
  Moon,
  Apple,
  ChevronDown,
  ChevronRight,
  Calendar
} from "lucide-react";
import { useState } from "react";

interface DietPlanProps {
  plan: Array<{
    day: string;
    breakfast: string;
    lunch: string;
    dinner: string;
    snack: string;
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  }>;
}

export const DietPlan = ({ plan }: DietPlanProps) => {
  const [expandedDays, setExpandedDays] = useState<Record<string, boolean>>({});

  const toggleDay = (day: string) => {
    setExpandedDays(prev => ({
      ...prev,
      [day]: !prev[day]
    }));
  };

  const getMealIcon = (mealType: string) => {
    switch (mealType.toLowerCase()) {
      case 'breakfast': return Coffee;
      case 'lunch': return Utensils;
      case 'dinner': return Moon;
      case 'snack': return Apple;
      default: return Utensils;
    }
  };

  const getMealColor = (mealType: string) => {
    switch (mealType.toLowerCase()) {
      case 'breakfast': return 'text-orange-600 bg-orange-50';
      case 'lunch': return 'text-green-600 bg-green-50';
      case 'dinner': return 'text-blue-600 bg-blue-50';
      case 'snack': return 'text-purple-600 bg-purple-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // Calculate total nutrition if available
  const totalCalories = plan.reduce((sum, day) => sum + (day.calories || 0), 0);
  const avgCaloriesPerDay = Math.round(totalCalories / plan.length);

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500">
            <ChefHat className="w-5 h-5 text-white" />
          </div>
          <span>7-Day Diet Plan</span>
          <Badge variant="outline" className="ml-auto">
            {plan.length} days
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Nutrition Summary */}
        {avgCaloriesPerDay > 0 && (
          <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <p className="text-lg font-bold text-green-600">{avgCaloriesPerDay}</p>
              <p className="text-xs text-muted-foreground">Avg Calories/Day</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-blue-600">25g</p>
              <p className="text-xs text-muted-foreground">Protein</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-orange-600">45g</p>
              <p className="text-xs text-muted-foreground">Carbs</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-purple-600">20g</p>
              <p className="text-xs text-muted-foreground">Fat</p>
            </div>
          </div>
        )}

        {/* Daily Diet Plans */}
        <div className="space-y-3">
          {plan.map((dayPlan, index) => {
            const isExpanded = expandedDays[dayPlan.day];

            return (
              <Card key={index} className="overflow-hidden">
                <Collapsible
                  open={isExpanded}
                  onOpenChange={() => toggleDay(dayPlan.day)}
                >
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full p-4 justify-between hover:bg-gray-50"
                    >
                      <div className="flex items-center gap-3">
                        <Calendar className="w-4 h-4" />
                        <span className="font-semibold">{dayPlan.day}</span>
                        {dayPlan.calories && (
                          <Badge variant="outline">
                            {dayPlan.calories} kcal
                          </Badge>
                        )}
                      </div>
                      {isExpanded ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>

                  <CollapsibleContent className="px-4 pb-4">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Meal Type</TableHead>
                          <TableHead>Food Items</TableHead>
                          <TableHead className="text-right">Calories</TableHead>
                          <TableHead className="text-right">Macros</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {[
                          { type: 'Breakfast', food: dayPlan.breakfast, calories: 400 },
                          { type: 'Lunch', food: dayPlan.lunch, calories: 500 },
                          { type: 'Dinner', food: dayPlan.dinner, calories: 600 },
                          { type: 'Snack', food: dayPlan.snack, calories: 200 }
                        ].map((meal, mealIndex) => {
                          const MealIcon = getMealIcon(meal.type);

                          return (
                            <TableRow key={mealIndex}>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <div className={`p-1 rounded ${getMealColor(meal.type)}`}>
                                    <MealIcon className="w-3 h-3" />
                                  </div>
                                  <span className="font-medium text-sm">{meal.type}</span>
                                </div>
                              </TableCell>
                              <TableCell className="text-sm">
                                {meal.food}
                              </TableCell>
                              <TableCell className="text-right text-sm">
                                {meal.calories} kcal
                              </TableCell>
                              <TableCell className="text-right text-xs text-muted-foreground">
                                P: 15g | C: 30g | F: 10g
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
