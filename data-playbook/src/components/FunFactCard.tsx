
import { Card } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface FunFactCardProps {
  fact: {
    metric: string;
    fact: string;
    emoji?: string;
    icon?: LucideIcon;
  };
}

export const FunFactCard = ({ fact }: FunFactCardProps) => {
  const IconComponent = fact.icon;

  return (
    <Card className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 hover:shadow-md transition-all duration-300">
      <div className="flex items-center gap-4">
        {/* Icon or Emoji */}
        <div className="flex-shrink-0">
          {IconComponent ? (
            <div className="p-3 rounded-full bg-blue-100">
              <IconComponent className="w-6 h-6 text-blue-600" />
            </div>
          ) : (
            <div className="text-3xl">{fact.emoji}</div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-sm text-blue-700 mb-1">
            {fact.metric}
          </h4>
          <p className="text-sm text-gray-700 leading-relaxed">
            {fact.fact}
          </p>
        </div>
      </div>
    </Card>
  );
};

// Enhanced Fun Facts Generator
export const generateCreativeFunFacts = (healthData: any) => {
  const facts = [];

  // Steps comparison
  if (healthData.steps && healthData.steps > 0) {
    const eiffelTowerClimbs = Math.round(healthData.steps / 1665); // Eiffel Tower has ~1665 steps
    facts.push({
      metric: "Daily Steps",
      fact: `${healthData.steps.toLocaleString()} steps = climbing Eiffel Tower ${eiffelTowerClimbs} times`,
      emoji: "🗼"
    });
  }

  // Calories comparison
  if (healthData.calories_kcal && healthData.calories_kcal > 0) {
    const phoneHours = Math.round(healthData.calories_kcal / 10); // Rough estimate: 1 kcal = 10 hours phone battery
    facts.push({
      metric: "Calories Burned",
      fact: `${healthData.calories_kcal.toLocaleString()} kcal = powering your phone for ${phoneHours} hours`,
      emoji: "🔋"
    });
  }

  // Distance comparison
  if (healthData.distance_km && healthData.distance_km > 0) {
    const marathons = (healthData.distance_km / 42.2).toFixed(1);
    facts.push({
      metric: "Distance Covered",
      fact: `${healthData.distance_km} km = ${marathons} marathon distances`,
      emoji: "🏃‍♂️"
    });
  }

  // Water comparison
  if (healthData.water_liters && healthData.water_liters > 0) {
    const bottles = Math.round(healthData.water_liters / 0.5); // 500ml bottles
    facts.push({
      metric: "Water Intake",
      fact: `${healthData.water_liters}L = ${bottles} standard water bottles`,
      emoji: "💧"
    });
  }

  // Sleep comparison
  if (healthData.sleep_hours && healthData.sleep_hours > 0) {
    const percentage = Math.round((healthData.sleep_hours / 24) * 100);
    facts.push({
      metric: "Sleep Time",
      fact: `${healthData.sleep_hours} hours = ${percentage}% of your day in dreamland`,
      emoji: "😴"
    });
  }

  // Return max 5 facts as per requirements
  return facts.slice(0, 5);
};
