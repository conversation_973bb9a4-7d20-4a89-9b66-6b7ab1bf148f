# Data Playbook - Codebase Context

## Project Overview
This is a React-based data playbook application built with modern web technologies including TypeScript, Tailwind CSS, and various UI component libraries.

## Key Dependencies & Libraries

### UI Framework & Components
- **React 19** - Core UI framework
- **@radix-ui/react-context** - Context management utilities
- **@radix-ui/react-context-menu** - Context menu components
- **react-day-picker** - Date picker components
- **Tailwind CSS** - Utility-first CSS framework

### Development Tools
- **TypeScript** - Type safety and better developer experience
- **ESLint** - Code linting and quality enforcement
- **Autoprefixer** - CSS vendor prefixing
- **Sucrase** - Fast TypeScript/JSX transformer
- **Jiti** - Runtime TypeScript execution

### Build & Bundling
- **Babel** - JavaScript compilation
- **Acorn JSX** - JSX parsing
- **ts-interface-checker** - Runtime type checking

## Architecture Patterns

### Context Management
The project uses Radix UI's context system for state management:
- `createContext()` - Creates typed React contexts
- `createContextScope()` - Advanced context scoping for complex components
- Context providers with automatic error boundaries

### Component Structure
- Modular component architecture
- Context-based state sharing
- Scoped styling with Tailwind CSS

## Key Features Implemented

### 1. Context System
- **Location**: `node_modules/@radix-ui/react-context/`
- **Purpose**: Type-safe context creation and consumption
- **Key Functions**:
  - `createContext(rootComponentName, defaultContext)` - Basic context creation
  - `createContextScope(scopeName, deps)` - Advanced scoped contexts
  - Automatic error handling for missing providers

### 2. Context Menu System
- **Location**: `node_modules/@radix-ui/react-context-menu/`
- **Components Available**:
  - `ContextMenu` - Root component
  - `ContextMenuTrigger` - Trigger element
  - `ContextMenuContent` - Menu content container
  - `ContextMenuItem` - Individual menu items
  - `ContextMenuSeparator` - Visual separators
  - `ContextMenuCheckboxItem` - Checkbox menu items
  - `ContextMenuRadioGroup/RadioItem` - Radio button groups

### 3. Date Picker Integration
- **Location**: `node_modules/react-day-picker/`
- **Features**:
  - Context-based configuration
  - Modifiers system for date styling
  - Formatters for internationalization
  - Labels for accessibility

### 4. Styling System
- **Tailwind CSS** integration with context-aware utilities
- **Autoprefixer** for cross-browser compatibility
- Utility-first approach with component-scoped styles

## Development Workflow

### Code Quality
- **ESLint** configuration with strict rules
- **TypeScript** for type safety
- **Prettier** formatting (implied by build tools)

### Build Process
- **Babel** for JavaScript transformation
- **Sucrase** for fast TypeScript compilation
- **Jiti** for runtime TypeScript execution during development

## File Structure Patterns

```
node_modules/
├── @radix-ui/
│   ├── react-context/          # Context utilities
│   └── react-context-menu/     # Context menu components
├── react-day-picker/           # Date picker components
├── tailwindcss/               # CSS framework
└── [other dependencies]/
```

## Key APIs to Know

### Context Creation
```typescript
// Basic context
const [Provider, useContext] = createContext('ComponentName', defaultValue);

// Scoped context
const [createScopedContext, createScope] = createContextScope('scopeName');
```

### Context Menu Usage
```typescript
import * as ContextMenu from '@radix-ui/react-context-menu';

// Basic structure
<ContextMenu.Root>
  <ContextMenu.Trigger>Right click me</ContextMenu.Trigger>
  <ContextMenu.Content>
    <ContextMenu.Item>Menu Item</ContextMenu.Item>
  </ContextMenu.Content>
</ContextMenu.Root>
```

## Development Guidelines

### Type Safety
- All contexts are strongly typed
- Use TypeScript interfaces for component props
- Leverage type inference where possible

### Error Handling
- Context providers automatically throw descriptive errors
- Use error boundaries for graceful degradation
- Validate props at runtime when needed

### Performance
- Context values are memoized automatically
- Use React.useMemo for expensive computations
- Leverage code splitting for large components

## Next Steps for Junior Developers

1. **Start with Context System** - Understand how `createContext` works
2. **Explore Component Examples** - Look at existing Radix UI implementations
3. **Practice with Date Picker** - Implement calendar features
4. **Master Tailwind** - Learn utility classes and responsive design
5. **Study Error Patterns** - Understand how context errors are handled

## Debugging Tips

- Use React DevTools to inspect context values
- Check console for context provider errors
- Verify Tailwind classes are being applied correctly
- Use TypeScript compiler for early error detection

## Resources

- [Radix UI Documentation](https://radix-ui.com/primitives)
- [React Day Picker Docs](https://react-day-picker.js.org/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)